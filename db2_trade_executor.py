#!/usr/bin/env python3
"""
DB2 Trade Executor - Main DB2 Engine for Trade Execution
Consolidates: Signal confirmation + Trade execution + Database operations

Responsibilities:
1. Receive signals from DB1 via millisecond communication
2. Start rolling window confirmations (R+R for BUY, F+F for SELL)
3. Execute trades immediately upon confirmation
4. Manage active positions and send updates to DB1
5. Handle paper trading with complete audit trail
6. Database operations for DB2 (trading_operations.db)

WORKS INDEPENDENTLY - Gets data every 2 minutes and executes BUY/SELL
"""

import sqlite3
import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
from db1_db2_communicator import TradingSignal, ActivePosition, get_communicator
from rolling_window_manager import get_rolling_window_manager

class DB2_TradeExecutor:
    """
    DB2 Trade Executor - Main DB2 Engine (Consolidated)

    Responsibilities:
    1. Receive signals from DB1 via millisecond communication
    2. Start rolling window confirmations (R+R for BUY, F+F for SELL)
    3. Execute trades immediately upon confirmation
    4. Manage active positions and send updates to DB1
    5. Handle paper trading with complete audit trail
    6. Database operations for DB2 (trading_operations.db)
    7. Monitor ₹800 profit target and send SELL signals back to DB1

    WORKS INDEPENDENTLY - Gets data every 2 minutes and executes BUY/SELL
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = 'Data/trading_operations.db'
        self.communicator = get_communicator()
        self.rolling_window_manager = get_rolling_window_manager()

        # Active positions managed by DB2
        self.active_positions = {}

        # Trading parameters
        self.investment_per_symbol = 100000  # ₹100,000 per symbol (₹1 Lakh as specified)
        self.profit_target = 800  # ₹800 profit target (₹1,00,800 total when reached)

        # Initialize database
        self._init_db2_tables()
        self._load_active_positions_from_db2()

        self.logger.info("✅ DB2 Trade Executor initialized - Independent trade execution engine")
        self.logger.info(f"💰 Profit target: ₹{self.profit_target} per position")
        self.logger.info(f"📊 Loaded {len(self.active_positions)} active positions from DB2")
    
    def run_periodic_check(self):
        """
        Periodic DB2 check - called every 15 minutes by the main system
        1. Check for signals from DB1
        2. Monitor active positions for ₹800 profit target
        3. Send position updates to DB1
        4. Cleanup completed rolling windows
        """
        try:
            self.logger.info("🔄 DB2 PERIODIC CHECK (15-minute interval)")

            # Step 1: Check for any pending signals from DB1
            signals_processed = 0
            while True:
                signal = self.communicator.receive_signal_from_db1(timeout_ms=10)
                if not signal:
                    break

                self.logger.info(f"📥 SIGNAL RECEIVED FROM DB1: {signal.signal_type} {signal.symbol} @ ₹{signal.price:.2f}")
                self._process_signal_from_db1(signal)
                signals_processed += 1

            if signals_processed > 0:
                self.logger.info(f"✅ Processed {signals_processed} signals from DB1")

            # Step 2: Monitor active positions for ₹800 profit target (SIMPLIFIED FLOW)
            self._monitor_profit_targets()

            # Step 3: SIMPLIFIED FLOW - No need to send positions back to DB1

            # Step 4: Cleanup completed rolling windows
            self.rolling_window_manager.cleanup_completed_monitors()

            self.logger.info(f"✅ DB2 periodic check completed - {len(self.active_positions)} active positions")

        except Exception as e:
            self.logger.error(f"❌ Error in DB2 periodic check: {e}")

    def run_trade_execution_loop(self):
        """
        DEPRECATED: Use run_periodic_check() instead
        This method is kept for backward compatibility but should not be used
        """
        self.logger.warning("⚠️ run_trade_execution_loop() is deprecated - use run_periodic_check() instead")
        self.logger.info("🔄 DB2 running in EVENT-DRIVEN mode - waiting for periodic calls")

        # Just run one periodic check and exit
        self.run_periodic_check()
    
    def _process_signal_from_db1(self, signal: TradingSignal):
        """Process signal received from DB1 and start rolling window confirmation"""
        try:
            if signal.signal_type == 'BUY':
                self._process_buy_signal(signal)
            elif signal.signal_type == 'SELL':
                self._process_sell_signal(signal)
            else:
                self.logger.error(f"❌ Unknown signal type: {signal.signal_type}")
        
        except Exception as e:
            self.logger.error(f"❌ Error processing signal from DB1: {e}")
    
    def _process_buy_signal(self, signal: TradingSignal):
        """Process BUY signal - SIMPLIFIED FLOW: Execute immediately, no RR confirmation needed"""
        try:
            # Check if symbol already has active position
            if signal.symbol in self.active_positions:
                self.logger.warning(f"⚠️ BUY signal ignored - {signal.symbol} already has active position")
                return

            self.logger.info(f"🎯 SIMPLIFIED FLOW: Executing BUY immediately for {signal.symbol}")
            self.logger.info(f"💡 No RR confirmation needed - Direct execution")

            # Execute BUY trade immediately using signal price
            self._execute_buy_trade_immediately(signal)

        except Exception as e:
            self.logger.error(f"❌ Error processing BUY signal: {e}")

    def _execute_buy_trade_immediately(self, signal: TradingSignal):
        """Execute BUY trade immediately without RR confirmation - SIMPLIFIED FLOW"""
        try:
            symbol = signal.symbol
            execution_price = signal.price
            execution_time = datetime.now()

            self.logger.info(f"🎯 EXECUTING BUY IMMEDIATELY: {symbol} @ ₹{execution_price:.2f}")

            # Calculate trade parameters
            shares_quantity = int(self.investment_per_symbol / execution_price)
            actual_investment = shares_quantity * execution_price
            target_price = execution_price + (self.profit_target / shares_quantity)

            # Create active position
            position = ActivePosition(
                symbol=symbol,
                buy_price=execution_price,
                shares_quantity=shares_quantity,
                investment=actual_investment,
                target_price=target_price,
                buy_time=execution_time,
                current_profit=0.0,
                status='ACTIVE',
                timestamp_ns=time.time_ns()
            )

            # Store position in DB2
            if self._store_position_in_db2(position):
                # Add to active positions
                self.active_positions[symbol] = position

                # Log paper trade
                self._log_paper_trade(symbol, execution_price, 'BUY', shares_quantity, actual_investment)

                # Start FF monitoring for this position (every 2 minutes)
                self._start_ff_monitoring(symbol)

                self.logger.info(f"✅ BUY EXECUTED IMMEDIATELY: {symbol}")
                self.logger.info(f"   Shares: {shares_quantity} @ ₹{execution_price:.2f}")
                self.logger.info(f"   Investment: ₹{actual_investment:.0f}")
                self.logger.info(f"   Target: ₹{target_price:.2f} (₹{self.profit_target:.0f} profit)")
                self.logger.info(f"🔄 Started FF monitoring + ₹800 profit monitoring")

            else:
                self.logger.error(f"❌ Failed to store position for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error executing immediate BUY trade: {e}")

    def _start_ff_monitoring(self, symbol: str):
        """Start FF pattern monitoring for sell conditions - SIMPLIFIED FLOW"""
        try:
            self.logger.info(f"🔄 Starting FF monitoring for {symbol} (every 2 minutes)")

            # Start rolling window monitor for F+F confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=symbol,
                signal_type='SELL',
                base_price=self.active_positions[symbol].buy_price,
                callback=lambda symbol, confirmation_price, confirmation_time, data_points:
                    self._on_ff_sell_confirmation(symbol, confirmation_price, confirmation_time, data_points)
            )

            if success:
                self.logger.info(f"✅ FF monitoring started for {symbol}")
            else:
                self.logger.error(f"❌ Failed to start FF monitoring for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error starting FF monitoring: {e}")

    def _process_sell_signal(self, signal: TradingSignal):
        """Process SELL signal and start F+F rolling window confirmation"""
        try:
            # Check if symbol has active position
            if signal.symbol not in self.active_positions:
                self.logger.warning(f"⚠️ SELL signal ignored - {signal.symbol} has no active position")
                return
            
            self.logger.info(f"🔄 Starting F+F rolling window confirmation for {signal.symbol}")
            
            # Start rolling window monitor for F+F confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=signal.symbol,
                signal_type='SELL',
                base_price=signal.price,
                callback=lambda symbol, confirmation_price, confirmation_time, data_points:
                    self._on_sell_confirmation(symbol, signal, confirmation_price, confirmation_time, data_points)
            )
            
            if success:
                self.logger.info(f"✅ F+F rolling window started for {signal.symbol}")
                
                # Send confirmation status to DB1
                self.communicator.send_confirmation_to_db1(
                    signal.symbol,
                    'SELL_CONFIRMATION_STARTED', 
                    {'base_price': signal.price, 'pattern_required': 'F+F'}
                )
            else:
                self.logger.error(f"❌ Failed to start F+F rolling window for {signal.symbol}")
        
        except Exception as e:
            self.logger.error(f"❌ Error processing SELL signal: {e}")
    
    # REMOVED: _on_buy_confirmation - Not needed in simplified flow
    # BUY is executed immediately without RR confirmation
    
    def _on_ff_sell_confirmation(self, symbol: str, confirmation_price: float,
                               confirmation_time: datetime, data_points: List):
        """Callback when F+F pattern confirmed - Execute SELL trade immediately - SIMPLIFIED FLOW"""
        try:
            self.logger.info(f"🎯 FF PATTERN CONFIRMED: Executing SELL for {symbol} @ ₹{confirmation_price:.2f}")
            self.logger.info(f"📉 Continuous FF detected every 2 minutes - SELL condition met")

            # Get active position
            position = self.active_positions.get(symbol)
            if not position:
                self.logger.error(f"❌ No active position found for {symbol}")
                return

            # Calculate profit
            total_value = position.shares_quantity * confirmation_price
            actual_profit = total_value - position.investment

            # Update position in DB2
            if self._close_position_in_db2(symbol, confirmation_price, actual_profit, confirmation_time):
                # Log paper trade
                self._log_paper_trade(symbol, confirmation_price, 'SELL', position.shares_quantity, total_value)

                # Remove from active positions
                del self.active_positions[symbol]

                self.logger.info(f"✅ SELL EXECUTED (FF Pattern): {symbol} - {position.shares_quantity} shares @ ₹{confirmation_price:.2f}")
                self.logger.info(f"💰 Profit: ₹{actual_profit:.0f} ({(actual_profit/position.investment)*100:.1f}%)")
                self.logger.info(f"📉 Reason: Continuous FF pattern detected")

            else:
                self.logger.error(f"❌ Failed to close position for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error executing FF SELL trade: {e}")

    def _on_sell_confirmation(self, symbol: str, signal: TradingSignal, confirmation_price: float,
                            confirmation_time: datetime, data_points: List):
        """Legacy callback - kept for compatibility"""
        self._on_ff_sell_confirmation(symbol, confirmation_price, confirmation_time, data_points)
    
    # REMOVED: _update_and_send_positions_to_db1 - Not needed in simplified flow
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol (simulate for now)"""
        try:
            # TODO: Replace with actual real-time price API
            # For now, get latest price from trading_data
            conn = sqlite3.connect('Data/trading_data.db')
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT close_price FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 1
            ''', (symbol,))
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else None
        
        except Exception as e:
            self.logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return None
    
    def _store_position_in_db2(self, position: ActivePosition) -> bool:
        """Store active position in DB2 database with proper calculations"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Calculate target_value (investment + profit target)
            target_value = position.investment + self.profit_target

            cursor.execute('''
            INSERT OR REPLACE INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_value, target_price, buy_time, status, actual_profit, profit_amount)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                position.symbol,
                position.buy_price,
                position.shares_quantity,
                position.investment,
                target_value,
                position.target_price,
                position.buy_time.isoformat() if hasattr(position.buy_time, 'isoformat') else str(position.buy_time),
                position.status,
                0.0,  # initial actual_profit
                0.0   # initial profit_amount
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"💾 Position stored: {position.symbol} - {position.shares_quantity} shares @ ₹{position.buy_price:.2f}")
            self.logger.info(f"   Investment: ₹{position.investment:.0f}, Target: ₹{target_value:.0f}")

            return True

        except Exception as e:
            self.logger.error(f"❌ Error storing position in DB2: {e}")
            return False
    
    def _close_position_in_db2(self, symbol: str, sell_price: float, profit: float, sell_time: datetime) -> bool:
        """Close position in DB2 database with proper profit calculations"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
            UPDATE trading_positions
            SET sell_price = ?, actual_profit = ?, profit_amount = ?, sell_time = ?, status = 'SOLD'
            WHERE symbol = ? AND status = 'ACTIVE'
            ''', (
                sell_price,
                profit,
                profit,  # Both actual_profit and profit_amount get the same value
                sell_time.isoformat() if hasattr(sell_time, 'isoformat') else str(sell_time),
                symbol
            ))

            rows_affected = cursor.rowcount
            conn.commit()
            conn.close()

            if rows_affected > 0:
                self.logger.info(f"✅ Position closed: {symbol} @ ₹{sell_price:.2f} - Profit: ₹{profit:.0f}")
                return True
            else:
                self.logger.error(f"❌ No active position found to close for {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error closing position in DB2: {e}")
            return False
    
    def _log_paper_trade(self, symbol: str, price: float, action: str, quantity: int, amount: float):
        """Log paper trade to CSV file"""
        try:
            import csv
            import os
            
            csv_file = 'Data/csv_files/paper_trade.csv'
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(csv_file), exist_ok=True)
            
            # Check if file exists to write header
            file_exists = os.path.exists(csv_file)
            
            with open(csv_file, 'a', newline='') as file:
                writer = csv.writer(file)
                
                if not file_exists:
                    writer.writerow(['Timestamp', 'Symbol', 'Action', 'Price', 'Quantity', 'Amount', 'API_Call'])
                
                api_call = f"smart_api.placeOrder(symbol='{symbol}', transactiontype='{action}', quantity={quantity}, price={price}, ordertype='MARKET', producttype='INTRADAY')"
                
                writer.writerow([
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    symbol,
                    action,
                    f"{price:.2f}",
                    quantity,
                    f"{amount:.2f}",
                    api_call
                ])
            
            self.logger.info(f"📝 Paper trade logged: {action} {symbol} @ ₹{price:.2f}")
        
        except Exception as e:
            self.logger.error(f"❌ Error logging paper trade: {e}")
    
    def _init_db2_tables(self):
        """Initialize DB2 database tables with proper schema"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Trading positions table with standardized schema
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                sell_price REAL,
                shares_quantity INTEGER NOT NULL,
                investment REAL NOT NULL,
                target_value REAL NOT NULL,
                target_price REAL NOT NULL,
                buy_time DATETIME NOT NULL,
                sell_time DATETIME,
                actual_profit REAL DEFAULT 0,
                profit_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'ACTIVE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, buy_time)
            )
            ''')

            # Add missing columns if they don't exist (for existing databases)
            try:
                cursor.execute('ALTER TABLE trading_positions ADD COLUMN target_value REAL DEFAULT 0')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE trading_positions ADD COLUMN profit_amount REAL DEFAULT 0')
            except sqlite3.OperationalError:
                pass  # Column already exists

            conn.commit()
            conn.close()

            self.logger.info("✅ DB2 tables initialized with standardized schema")

        except Exception as e:
            self.logger.error(f"❌ Error initializing DB2 tables: {e}")

    def _load_active_positions_from_db2(self):
        """Load active positions from DB2 database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
            SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time
            FROM trading_positions
            WHERE status = 'ACTIVE'
            ''')

            rows = cursor.fetchall()
            conn.close()

            for row in rows:
                symbol, buy_price, shares_quantity, investment, target_price, buy_time = row

                # Create ActivePosition object
                position = ActivePosition(
                    symbol=symbol,
                    buy_price=buy_price,
                    shares_quantity=shares_quantity,
                    investment=investment,
                    target_price=target_price,
                    buy_time=datetime.fromisoformat(buy_time) if isinstance(buy_time, str) else buy_time,
                    current_profit=0.0,
                    status='ACTIVE',
                    timestamp_ns=time.time_ns()
                )

                self.active_positions[symbol] = position

            self.logger.info(f"✅ Loaded {len(self.active_positions)} active positions from DB2")

        except Exception as e:
            self.logger.error(f"❌ Error loading active positions from DB2: {e}")

    def _monitor_profit_targets(self):
        """Monitor active positions for ₹800 profit target - SIMPLIFIED FLOW: Execute SELL immediately"""
        try:
            if not self.active_positions:
                return

            for symbol, position in list(self.active_positions.items()):
                try:
                    # Get current price
                    current_price = self._get_current_price(symbol)

                    if current_price:
                        # Calculate current profit
                        current_value = position.shares_quantity * current_price
                        current_profit = current_value - position.investment

                        # Update position profit
                        position.current_profit = current_profit

                        # Check if profit target reached (₹800)
                        if current_profit >= self.profit_target:
                            self.logger.info(f"🎯 PROFIT TARGET REACHED: {symbol}")
                            self.logger.info(f"   Current profit: ₹{current_profit:.0f} (target: ₹{self.profit_target})")
                            self.logger.info(f"   Current price: ₹{current_price:.2f}")
                            self.logger.info(f"💰 SIMPLIFIED FLOW: Executing SELL immediately")

                            # Execute SELL immediately (no need to send back to DB1)
                            self._execute_profit_target_sell(symbol, current_price, current_profit)

                except Exception as e:
                    self.logger.error(f"❌ Error monitoring profit for {symbol}: {e}")

        except Exception as e:
            self.logger.error(f"❌ Error in profit monitoring: {e}")

    def _execute_profit_target_sell(self, symbol: str, current_price: float, current_profit: float):
        """Execute SELL immediately when profit target reached - SIMPLIFIED FLOW"""
        try:
            # Get active position
            position = self.active_positions.get(symbol)
            if not position:
                self.logger.error(f"❌ No active position found for {symbol}")
                return

            sell_time = datetime.now()
            total_value = position.shares_quantity * current_price

            # Update position in DB2
            if self._close_position_in_db2(symbol, current_price, current_profit, sell_time):
                # Log paper trade
                self._log_paper_trade(symbol, current_price, 'SELL', position.shares_quantity, total_value)

                # Remove from active positions
                del self.active_positions[symbol]

                self.logger.info(f"✅ SELL EXECUTED (Profit Target): {symbol} - {position.shares_quantity} shares @ ₹{current_price:.2f}")
                self.logger.info(f"💰 Profit: ₹{current_profit:.0f} ({(current_profit/position.investment)*100:.1f}%)")
                self.logger.info(f"🎯 Reason: ₹800 profit target reached")

            else:
                self.logger.error(f"❌ Failed to close position for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error executing profit target SELL: {e}")

    def _fetch_data_independently(self):
        """Fetch data every 2 minutes independently for DB2 operations"""
        try:
            from datetime import datetime

            # Check if it's trading hours (9:15 AM - 3:30 PM)
            now = datetime.now()
            market_start = now.replace(hour=9, minute=15, second=0, microsecond=0)
            market_end = now.replace(hour=15, minute=30, second=0, microsecond=0)

            if not (market_start <= now <= market_end):
                self.logger.info("🕐 Outside trading hours - skipping independent data fetch")
                return

            self.logger.info("📊 DB2 INDEPENDENT DATA FETCH - Every 2 minutes")

            # Get current prices for active positions (simulate real-time data)
            for symbol in self.active_positions.keys():
                try:
                    current_price = self._get_current_price(symbol)
                    if current_price:
                        self.logger.debug(f"📈 {symbol}: ₹{current_price:.2f}")
                except Exception as e:
                    self.logger.error(f"❌ Error fetching price for {symbol}: {e}")

            self.logger.info(f"✅ DB2 independent data fetch completed for {len(self.active_positions)} active positions")

        except Exception as e:
            self.logger.error(f"❌ Error in independent data fetch: {e}")

    # REMOVED: _send_sell_signal_to_db1 - Not needed in simplified flow
    # DB2 executes SELL immediately when conditions are met

    def get_status(self) -> Dict[str, any]:
        """Get current status of DB2 trade executor"""
        total_profit = sum(pos.current_profit for pos in self.active_positions.values())
        profitable_positions = sum(1 for pos in self.active_positions.values() if pos.current_profit > 0)

        return {
            'active_positions': len(self.active_positions),
            'profitable_positions': profitable_positions,
            'total_current_profit': total_profit,
            'profit_target': self.profit_target,
            'active_monitors': len(self.rolling_window_manager.get_active_monitors()),
            'rolling_window_status': self.rolling_window_manager.get_active_monitors(),
            'communicator_status': self.communicator.get_queue_status(),
            'performance_stats': self.communicator.get_performance_stats(),
            'last_update': datetime.now().isoformat()
        }

# Global instance
_db2_trade_executor = None

def get_db2_trade_executor() -> DB2_TradeExecutor:
    """Get global DB2 trade executor instance"""
    global _db2_trade_executor
    if _db2_trade_executor is None:
        _db2_trade_executor = DB2_TradeExecutor()
    return _db2_trade_executor
