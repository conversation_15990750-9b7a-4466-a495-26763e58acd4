#!/usr/bin/env python3
"""
Test Script for Simplified DB1-DB2 Flow
Demonstrates the new simplified trading flow where:
1. DB1 only detects 4F+1R patterns and sends to DB2
2. DB2 executes BUY immediately and monitors for:
   - ₹800 profit target (immediate SELL)
   - Continuous FF pattern every 2 minutes (immediate SELL)
"""

import logging
import time
from datetime import datetime
from db1_signal_generator import DB1_SignalGenerator
from db2_trade_executor import get_db2_trade_executor
from db1_db2_communicator import TradingSignal

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_simplified_flow():
    """Test the simplified DB1-DB2 flow"""
    print("=" * 80)
    print("🧪 TESTING SIMPLIFIED DB1-DB2 FLOW")
    print("=" * 80)
    
    # Initialize components
    db1_generator = DB1_SignalGenerator()
    db2_executor = get_db2_trade_executor()
    
    print("\n📋 SIMPLIFIED FLOW OVERVIEW:")
    print("1. DB1: Detects 4F+1R patterns → sends BUY signal to DB2")
    print("2. DB2: Executes BUY immediately (no RR confirmation)")
    print("3. DB2: Monitors for 2 SELL conditions:")
    print("   - ₹800 profit reached → immediate SELL")
    print("   - Continuous FF pattern every 2 minutes → immediate SELL")
    print("4. No back-and-forth communication between DB1 and DB2")
    
    # Test 1: Simulate DB1 pattern detection
    print("\n" + "=" * 50)
    print("🔍 TEST 1: DB1 Pattern Detection")
    print("=" * 50)
    
    test_symbol = "RELIANCE"
    test_price = 2500.0
    test_timestamp = datetime.now().isoformat()
    
    print(f"📊 Simulating 4F+1R pattern detection for {test_symbol}")
    print(f"💰 Signal price: ₹{test_price:.2f}")
    
    # Create test signal
    signal = TradingSignal(
        symbol=test_symbol,
        signal_type='BUY',
        price=test_price,
        timestamp_ns=time.time_ns(),
        pattern_info={
            'pattern_type': '4F+1R',
            'detection_method': 'test_simulation',
            'detection_time': test_timestamp,
            'drop_percentage': 1.2,
            'flow_type': 'SIMPLIFIED'
        },
        source='DB1_TEST'
    )
    
    # Send signal to DB2
    print(f"📤 Sending BUY signal from DB1 to DB2...")
    db1_generator.communicator.send_signal_to_db2(signal)
    
    # Test 2: DB2 processes signal
    print("\n" + "=" * 50)
    print("🔄 TEST 2: DB2 Signal Processing")
    print("=" * 50)
    
    print("⏳ Waiting for DB2 to process signal...")
    time.sleep(2)
    
    # Process signals in DB2
    signals_processed = 0
    while True:
        received_signal = db2_executor.communicator.receive_signal_from_db1(timeout_ms=100)
        if not received_signal:
            break
        
        print(f"📥 DB2 received signal: {received_signal.signal_type} {received_signal.symbol}")
        db2_executor._process_signal_from_db1(received_signal)
        signals_processed += 1
    
    print(f"✅ DB2 processed {signals_processed} signals")
    
    # Test 3: Check DB2 status
    print("\n" + "=" * 50)
    print("📊 TEST 3: DB2 Status Check")
    print("=" * 50)
    
    status = db2_executor.get_status()
    print(f"🔢 Active positions: {status['active_positions']}")
    print(f"💰 Total current profit: ₹{status['total_current_profit']:.2f}")
    print(f"🎯 Profit target: ₹{status['profit_target']}")
    print(f"🔄 Active monitors: {status['active_monitors']}")
    
    # Test 4: Simulate profit monitoring
    print("\n" + "=" * 50)
    print("💰 TEST 4: Profit Monitoring")
    print("=" * 50)
    
    print("🔄 Running profit monitoring cycle...")
    db2_executor._monitor_profit_targets()
    
    # Test 5: Show final status
    print("\n" + "=" * 50)
    print("📈 TEST 5: Final Status")
    print("=" * 50)
    
    final_status = db2_executor.get_status()
    print(f"🔢 Final active positions: {final_status['active_positions']}")
    print(f"💰 Final total profit: ₹{final_status['total_current_profit']:.2f}")
    
    print("\n" + "=" * 80)
    print("✅ SIMPLIFIED FLOW TEST COMPLETED")
    print("=" * 80)
    
    print("\n📋 KEY DIFFERENCES FROM OLD FLOW:")
    print("❌ OLD: DB1 → DB2 → wait for RR → BUY → send position to DB1 → DB1 monitors → send SELL to DB2 → wait for FF → SELL")
    print("✅ NEW: DB1 → DB2 → BUY immediately → DB2 monitors (₹800 profit + FF pattern) → SELL immediately")
    print("\n🎯 BENEFITS:")
    print("• Faster execution (no RR confirmation delay)")
    print("• Simpler architecture (no back-and-forth communication)")
    print("• All execution logic in one place (DB2)")
    print("• Reduced complexity and potential failure points")

def test_pattern_detection():
    """Test DB1 pattern detection in isolation"""
    print("\n" + "=" * 50)
    print("🔍 TESTING DB1 PATTERN DETECTION")
    print("=" * 50)
    
    db1_generator = DB1_SignalGenerator()
    
    # Simulate pattern detection
    test_symbols = ["RELIANCE", "TCS", "INFY"]
    
    for symbol in test_symbols:
        print(f"\n📊 Testing pattern detection for {symbol}")
        
        # Simulate price data that would trigger 4F+1R
        prices = [2500.0, 2480.0, 2460.0, 2440.0, 2420.0, 2450.0]  # 4F+1R pattern
        
        for i, price in enumerate(prices):
            timestamp = datetime.now().isoformat()
            print(f"   {i+1}. Price: ₹{price:.2f}")
            
            if i > 0:
                # Check for pattern
                pattern_detected = db1_generator.detect_rolling_window_pattern_realtime(
                    symbol, price, timestamp
                )
                
                if pattern_detected:
                    print(f"   🎯 4F+1R PATTERN DETECTED for {symbol}!")
                    break

if __name__ == "__main__":
    print("🚀 Starting Simplified Flow Tests...")
    
    # Test 1: Pattern detection
    test_pattern_detection()
    
    # Test 2: Full flow
    test_simplified_flow()
    
    print("\n🎉 All tests completed!")
