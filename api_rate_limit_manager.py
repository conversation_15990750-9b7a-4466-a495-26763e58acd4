#!/usr/bin/env python3
"""
API Rate Limit Manager
Handles API rate limiting with intelligent retry logic for both DB1 and DB2
Implements 5-10-15 second retry cycles until success
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Callable, Any, Optional
import functools

class APIRateLimitManager:
    def __init__(self, name: str = "API"):
        self.name = name
        self.logger = logging.getLogger(f"APIRateLimit_{name}")
        self.retry_delays = [5, 10, 15]  # 5, 10, 15 seconds retry cycle
        self.max_retries = 100  # Retry until success
        self.last_api_call = None
        self.api_call_count = 0
        self.rate_limit_hits = 0
        
    def with_rate_limit(self, func: Callable) -> Callable:
        """Decorator to add rate limiting to any function"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self._execute_with_retry(func, *args, **kwargs)
        return wrapper
    
    def _execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with intelligent retry logic"""
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                # Track API call
                self.api_call_count += 1
                self.last_api_call = datetime.now()
                
                # Execute the function
                result = func(*args, **kwargs)
                
                # Success - reset retry count and return
                if retry_count > 0:
                    self.logger.info(f"✅ {self.name} API call succeeded after {retry_count} retries")
                
                return result
                
            except Exception as e:
                error_msg = str(e).lower()
                
                # Check if it's a rate limit error
                if any(keyword in error_msg for keyword in [
                    'rate limit', 'too many requests', 'quota exceeded', 
                    'api limit', 'throttle', '429', 'limit exceeded'
                ]):
                    self.rate_limit_hits += 1
                    retry_count += 1
                    
                    # Calculate delay using cycle pattern
                    delay_index = (retry_count - 1) % len(self.retry_delays)
                    delay = self.retry_delays[delay_index]
                    
                    # Don't log the error - just the retry info
                    self.logger.info(f"🔄 {self.name} API rate limit hit - Retry {retry_count}/{self.max_retries} in {delay}s")
                    
                    time.sleep(delay)
                    continue
                    
                else:
                    # Non-rate-limit error - log and re-raise
                    self.logger.error(f"❌ {self.name} API error (non-rate-limit): {e}")
                    raise e
        
        # If we get here, we've exhausted all retries
        self.logger.error(f"❌ {self.name} API failed after {self.max_retries} retries")
        raise Exception(f"{self.name} API rate limit exceeded maximum retries")
    
    def get_stats(self) -> dict:
        """Get rate limiting statistics"""
        return {
            'name': self.name,
            'total_api_calls': self.api_call_count,
            'rate_limit_hits': self.rate_limit_hits,
            'last_api_call': self.last_api_call.isoformat() if self.last_api_call else None,
            'success_rate': ((self.api_call_count - self.rate_limit_hits) / max(1, self.api_call_count)) * 100
        }
    
    def reset_stats(self):
        """Reset statistics"""
        self.api_call_count = 0
        self.rate_limit_hits = 0
        self.last_api_call = None

# Global instances for DB1 and DB2
db1_rate_limiter = APIRateLimitManager("DB1")
db2_rate_limiter = APIRateLimitManager("DB2")

def rate_limited_db1(func: Callable) -> Callable:
    """Decorator for DB1 API calls"""
    return db1_rate_limiter.with_rate_limit(func)

def rate_limited_db2(func: Callable) -> Callable:
    """Decorator for DB2 API calls"""
    return db2_rate_limiter.with_rate_limit(func)

class DataFetchManager:
    """Manages data fetching with rate limiting for both DB1 and DB2"""
    
    def __init__(self):
        self.logger = logging.getLogger("DataFetchManager")
        self.db1_limiter = db1_rate_limiter
        self.db2_limiter = db2_rate_limiter
    
    @rate_limited_db1
    def fetch_db1_data(self, data_fetcher, symbol: str, token: str, **kwargs) -> Optional[dict]:
        """Fetch data for DB1 with rate limiting"""
        try:
            # Check if this is a single candle fetch (realtime) or historical data
            current_time = kwargs.get('current_time')

            if current_time:
                # Single candle fetch for realtime data
                data = data_fetcher.fetch_latest_candle_for_symbol(symbol, token, current_time)
            else:
                # Historical data fetch
                data = data_fetcher.fetch_historical_data(
                    symbol=symbol,
                    token=token,
                    **kwargs
                )

            if data:
                if isinstance(data, list):
                    self.logger.info(f"✅ DB1: Retrieved {len(data)} records for {symbol}")
                else:
                    self.logger.info(f"✅ DB1: Retrieved candle data for {symbol}")
            else:
                self.logger.warning(f"⚠️ DB1: No data retrieved for {symbol}")

            return data

        except Exception as e:
            # Let the rate limiter handle retries for rate limit errors
            raise e
    
    @rate_limited_db2
    def fetch_db2_data(self, data_fetcher, symbol: str, token: str, **kwargs) -> Optional[list]:
        """Fetch data for DB2 with rate limiting"""
        try:
            self.logger.info(f"📊 DB2: Fetching 2-minute data for {symbol}")

            # Call the actual data fetcher method
            if hasattr(data_fetcher, 'fetch_historical_data'):
                data = data_fetcher.fetch_historical_data(
                    symbol=symbol,
                    token=token,
                    **kwargs
                )
            else:
                # Fallback to direct API call
                data = self._fetch_historical_data_direct(data_fetcher, symbol, token, **kwargs)

            if data:
                self.logger.info(f"✅ DB2: Retrieved {len(data)} records for {symbol}")
            else:
                self.logger.warning(f"⚠️ DB2: No data retrieved for {symbol}")

            return data

        except Exception as e:
            self.logger.error(f"❌ DB2: Data fetch failed for {symbol}: {e}")
            raise e

    def _fetch_historical_data_direct(self, data_fetcher, symbol: str, token: str, **kwargs) -> Optional[list]:
        """Direct historical data fetch for DB2"""
        try:
            from datetime import datetime

            start_time = kwargs.get('start_time')
            end_time = kwargs.get('end_time')
            interval = kwargs.get('interval', 'TWO_MINUTE')

            if not data_fetcher.smart_api:
                if not data_fetcher.connect_to_api():
                    return None

            # Prepare parameters
            params = {
                "exchange": "NSE",
                "symboltoken": token,
                "interval": interval,
                "fromdate": start_time.strftime("%Y-%m-%d %H:%M"),
                "todate": end_time.strftime("%Y-%m-%d %H:%M")
            }

            response = data_fetcher.smart_api.getCandleData(params)

            if response and response.get('status') and response.get('data'):
                candles = response['data']
                historical_data = []

                for candle in candles:
                    candle_time_str = candle[0].replace('Z', '+00:00')
                    candle_time = datetime.fromisoformat(candle_time_str)

                    historical_data.append({
                        'timestamp': candle_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'open': float(candle[1]),
                        'high': float(candle[2]),
                        'low': float(candle[3]),
                        'close': float(candle[4]),
                        'volume': int(candle[5])
                    })

                return historical_data

            return None

        except Exception as e:
            self.logger.error(f"❌ Direct historical data fetch failed: {e}")
            raise e
    
    def get_combined_stats(self) -> dict:
        """Get combined statistics for both DB1 and DB2"""
        db1_stats = self.db1_limiter.get_stats()
        db2_stats = self.db2_limiter.get_stats()
        
        return {
            'db1': db1_stats,
            'db2': db2_stats,
            'total_api_calls': db1_stats['total_api_calls'] + db2_stats['total_api_calls'],
            'total_rate_limit_hits': db1_stats['rate_limit_hits'] + db2_stats['rate_limit_hits']
        }

# Global instance
data_fetch_manager = DataFetchManager()

if __name__ == "__main__":
    # Test the rate limiter
    logging.basicConfig(level=logging.INFO)
    
    @rate_limited_db1
    def test_api_call():
        print("API call executed")
        return "success"
    
    # Test normal call
    result = test_api_call()
    print(f"Result: {result}")
    
    # Print stats
    stats = db1_rate_limiter.get_stats()
    print(f"Stats: {stats}")
